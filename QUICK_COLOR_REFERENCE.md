# 🎨 Quick Color Reference Card

## 🚀 Most Common Classes You'll Use

### Buttons
```jsx
// Primary button (main brand color)
className="bg-primary-600 hover:bg-primary-700 text-white"

// Secondary button (outline style)
className="border-2 border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-white"

// Disabled button
className="bg-neutral-400 text-white cursor-not-allowed"
```

### Text Colors
```jsx
// Headings
className="text-neutral-900"

// Body text
className="text-neutral-600"

// Muted/secondary text
className="text-neutral-400"

// Brand colored text
className="text-primary-600"

// Links
className="text-secondary-600 hover:text-secondary-700"
```

### Backgrounds
```jsx
// Light backgrounds
className="bg-neutral-50"    // Very light gray
className="bg-neutral-100"   // Light gray

// Card backgrounds
className="bg-white"

// Brand backgrounds
className="bg-primary-600"   // Main brand color
className="bg-primary-50"    // Very light brand color

// Gradients
className="bg-gradient-to-r from-primary-600 to-primary-700"
```

### Borders
```jsx
// Default borders
className="border border-neutral-300"

// Focus states (forms)
className="focus:border-primary-500 focus:ring-primary-500"

// Brand colored borders
className="border-primary-600"
```

## 🔄 How to Change Colors for New Projects

### Step 1: Edit `tailwind.config.js`
```javascript
// For a tech company (blue/purple theme)
primary: {
  50: '#eff6ff',
  // ... other shades
  600: '#2563eb',  // Main brand color (blue)
  700: '#1d4ed8',  // Hover state
},
secondary: {
  50: '#faf5ff',
  // ... other shades  
  600: '#7c3aed',  // Secondary color (purple)
  700: '#6d28d9',  // Hover state
},
```

### Step 2: That's it! 
All your components automatically use the new colors.

## 📋 Color Meanings

| Color Type | Purpose | Current Color | Usage |
|------------|---------|---------------|-------|
| `primary-*` | Main brand color | Orange | Buttons, CTAs, brand elements |
| `secondary-*` | Accent color | Blue | Links, secondary buttons, highlights |
| `neutral-*` | UI elements | Gray | Text, borders, backgrounds |
| `success-*` | Success states | Green | Success messages, checkmarks |
| `warning-*` | Warning states | Yellow | Warnings, caution |
| `error-*` | Error states | Red | Errors, validation |

## 🎯 Business Type Color Suggestions

### Construction/Carpentry (Current)
- Primary: Orange (`#ea580c`) - Warm, craftsmanship
- Secondary: Blue (`#2563eb`) - Professional, trustworthy

### Technology/Software
- Primary: Blue (`#2563eb`) - Professional, innovative  
- Secondary: Purple (`#7c3aed`) - Creative, modern

### Healthcare/Medical
- Primary: Blue (`#2563eb`) - Trustworthy, professional
- Secondary: Green (`#16a34a`) - Health, growth

### Finance/Legal
- Primary: Navy (`#1e40af`) - Professional, stable
- Secondary: Gold (`#d97706`) - Premium, success

### Real Estate
- Primary: Green (`#16a34a`) - Growth, prosperity
- Secondary: Blue (`#2563eb`) - Trust, stability

## 🛠️ Using the Color Utility

```jsx
import { colors, colorCombinations } from '@/utils/colors';

// Use predefined combinations
<button className={colorCombinations.primaryButton}>
  Click me
</button>

// Use individual color classes
<div className={colors.primary.bg.main}>
  Brand colored background
</div>

// Use gradients
<div className={gradients.primary}>
  Gradient background
</div>
```

## 🔍 Troubleshooting

### Colors not updating?
1. Restart dev server: `npm run dev`
2. Check if color classes are in the safelist
3. Clear browser cache

### Need a custom shade?
Add it to the safelist in `tailwind.config.js`:
```javascript
safelist: [
  // Add your custom classes here
  'bg-primary-550', 'text-primary-550',
]
```

## 📚 Files to Remember

- `tailwind.config.js` - Main color definitions
- `src/utils/colors.ts` - Color utility functions
- `src/index.css` - Custom component styles
- `COLOR_SYSTEM_GUIDE.md` - Detailed documentation
