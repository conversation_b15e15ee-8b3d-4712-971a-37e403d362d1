# 🎨 Tailwind Color System Guide

This project uses a custom Tailwind CSS color system designed for easy color swapping across different business types and branding needs.

## 🎯 Color Philosophy

The color system is built around **semantic color names** rather than specific color names (like "orange" or "blue"). This allows you to easily change the entire site's color scheme by updating just a few values in the Tailwind config.

## 🏗️ Color Structure

### Primary Colors (`primary-*`)
- **Purpose**: Main brand color, primary buttons, key CTAs, brand elements
- **Current**: Orange (perfect for construction/carpentry businesses)
- **Usage**: `bg-primary-600`, `text-primary-700`, `border-primary-500`

### Secondary Colors (`secondary-*`)
- **Purpose**: Secondary buttons, links, accents, supporting elements
- **Current**: Blue (professional, trustworthy)
- **Usage**: `bg-secondary-600`, `text-secondary-700`, `hover:text-secondary-600`

### Neutral Colors (`neutral-*`)
- **Purpose**: Text, backgrounds, borders, general UI elements
- **Current**: Gray scale (stays consistent across projects)
- **Usage**: `bg-neutral-50`, `text-neutral-900`, `border-neutral-300`

### Utility Colors
- **Success**: Green (`success-*`) - for success states, positive feedback
- **Warning**: Yellow (`warning-*`) - for warnings, caution states
- **Error**: Red (`error-*`) - for errors, destructive actions

## 🔄 How to Swap Colors for New Projects

### Step 1: Update Tailwind Config
Edit `tailwind.config.js` and change the color values in the `primary` and `secondary` objects:

```javascript
// Example: Changing to Blue/Green for a tech company
primary: {
  50: '#eff6ff',   // Very light blue
  100: '#dbeafe',  // Light blue
  // ... continue with blue scale
  600: '#2563eb',  // Main brand color
  700: '#1d4ed8',  // Darker shade
},
secondary: {
  50: '#f0fdf4',   // Very light green
  100: '#dcfce7',  // Light green
  // ... continue with green scale
  600: '#16a34a',  // Secondary brand color
  700: '#15803d',  // Darker shade
},
```

### Step 2: No Component Changes Needed!
Because we use semantic names (`primary-*`, `secondary-*`), all your components automatically use the new colors without any code changes.

## 📋 Color Usage Guidelines

### Primary Color Usage
```jsx
// Buttons - Primary actions
<button className="bg-primary-600 hover:bg-primary-700 text-white">
  Get Quote
</button>

// Brand elements
<h1 className="text-primary-800">Company Name</h1>

// Key highlights
<div className="bg-gradient-to-r from-primary-600 to-primary-700">
  Important content
</div>
```

### Secondary Color Usage
```jsx
// Secondary buttons
<button className="border-2 border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-white">
  Learn More
</button>

// Links
<a href="#" className="text-secondary-600 hover:text-secondary-700">
  Read more
</a>

// Accents
<div className="bg-secondary-50 border-l-4 border-secondary-500">
  Info box
</div>
```

### Neutral Color Usage
```jsx
// Text hierarchy
<h1 className="text-neutral-900">Main heading</h1>
<p className="text-neutral-600">Body text</p>
<span className="text-neutral-400">Muted text</span>

// Backgrounds
<div className="bg-neutral-50">Light background</div>
<div className="bg-neutral-100">Card background</div>

// Borders
<div className="border border-neutral-300">Card with border</div>
```

## 🎨 Pre-configured Color Schemes

### Construction/Carpentry (Current)
- **Primary**: Orange (`#ea580c`) - Warm, energetic, craftsmanship
- **Secondary**: Blue (`#2563eb`) - Professional, trustworthy

### Technology/Software
- **Primary**: Blue (`#2563eb`) - Professional, innovative
- **Secondary**: Purple (`#7c3aed`) - Creative, modern

### Healthcare/Medical
- **Primary**: Blue (`#2563eb`) - Trustworthy, professional
- **Secondary**: Green (`#16a34a`) - Health, growth

### Finance/Legal
- **Primary**: Navy (`#1e40af`) - Professional, stable
- **Secondary**: Gold (`#d97706`) - Premium, success

### Real Estate
- **Primary**: Green (`#16a34a`) - Growth, prosperity
- **Secondary**: Blue (`#2563eb`) - Trust, stability

## 🛠️ Implementation Examples

### Current Form Styling (Orange Theme)
```css
.custom-form-input {
  @apply focus:border-primary-500 focus:ring-primary-500;
}

.custom-form-button {
  @apply bg-primary-600 hover:bg-primary-700;
}

.custom-form-checkbox {
  @apply text-primary-600 bg-primary-50 focus:ring-primary-500;
}
```

### Navigation Styling
```jsx
// Logo color changes based on scroll state
<h1 className={`text-2xl font-bold ${
  isScrolled ? 'text-primary-800' : 'text-white'
}`}>
  Company Name
</h1>

// Navigation links
<a className={`${
  isScrolled 
    ? 'text-neutral-700 hover:text-primary-600' 
    : 'text-white hover:text-primary-200'
}`}>
  Nav Item
</a>
```

## 🚀 Quick Start for New Projects

1. **Copy this project** as your template
2. **Update `tailwind.config.js`** with your new brand colors
3. **Update company information** in components
4. **Test the color changes** - everything should automatically update!

## 📝 Color Accessibility

All color combinations in this system maintain WCAG AA contrast ratios:
- `primary-600` on white background: ✅ AA compliant
- `neutral-900` on white background: ✅ AAA compliant
- `secondary-600` on white background: ✅ AA compliant

## 🔍 Debugging Colors

If colors aren't updating after changes:
1. **Restart your dev server** (`npm run dev`)
2. **Check the safelist** in `tailwind.config.js` includes your color classes
3. **Verify color values** are valid hex codes
4. **Clear browser cache** if needed

## 📚 Additional Resources

- [Tailwind CSS Colors Documentation](https://tailwindcss.com/docs/customizing-colors)
- [Color Palette Generator](https://tailwindcss.com/docs/customizing-colors#color-palette-reference)
- [Accessibility Color Checker](https://webaim.org/resources/contrastchecker/)
